<template>
	<view class="checkin-box">
		<view>
			<view class="checkin">
				<text class="value"> {{ formatTime(checkin, 'M月D日') }}</text>
				<text>{{ checkinDay }}</text>
			</view>
			-
			<view class="checkout">
				<text class="value"> {{ formatTime(checkout, 'M月D日') }}</text>
				<text>{{checkoutDay}}</text>
			</view>
		</view>

		<view class="days">共{{days}}晚</view>
	</view>

</template>

<script setup>
	/**
	 * @property Date checkin
	 * @property Date checkout
	 */

	import {
		computed
	} from 'vue';
	import dayjs from 'dayjs';

	const props = defineProps({
		checkin: {
			type: Object,
			default: () => new Date(),
		},
		checkout: {
			type: Object,
			default: () => new Date(),
		}
	})

	const weekDaysShort = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

	const checkinDay = computed(() => {
		return props.checkin ? weekDaysShort[props.checkin.getDay()] : ''
	})
	const checkoutDay = computed(() => {
		return props.checkout ? weekDaysShort[props.checkout.getDay()] : ''
	})

	const days = computed(() => {
		return dayjs(props.checkout).diff(dayjs(props.checkin), 'day')
	})

	import {
		formatTime
	} from '../../utils/index.js'
</script>

<style scoped lang="scss">
	@import '../../styles/_mix.scss';
	@import '../../styles/_define.scss';

	.checkin-box {
		display: flex;
		align-items: flex-end;
		gap: $padding-middle;

		>view {
			&:first-child {
				@include center();
				gap: $padding-mini;
			}
		}
	}

	.checkin,
	.checkout {
		display: flex;
		flex-direction: column-reverse;
		font-weight: 500;

		text {
			font-weight: normal;
			color: $font-color-gray;
		}
	}
</style>