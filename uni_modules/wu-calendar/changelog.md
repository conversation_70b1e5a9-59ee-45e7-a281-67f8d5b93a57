## 1.5.6（2024-11-25）
1. 修复上版本日历高度错误的bug
2. 新增clearSelect方法用于清除日历选择
3. 新增todayDefaultStyle属性用于控制是否显示今日默认样式
## 1.5.5（2024-11-14）
1. 通过减少view数量优化日历性能
2. 修复nvue错误日历样式以及滑动时无法切换日历的bug
3. 修复nvue警告
## 1.5.4（2024-10-12）
修复忘记删除方法，导致运行错误
## 1.5.3（2024-10-12）
更新nvue下可能会造成日历重叠的bug
## 1.5.2（2024-06-14）
修复错误style语法
## 1.5.1（2024-06-11）
修复弹窗确认与取消设置颜色不生效
## 1.5.0（2024-06-03）
1. 修复monthSwitch事件初始化时被触发的问题
2. monthSwitch事件新增fullDate属性(用来方便直接获取字符串形式的完整日期)
3. 日历新增confirmFullDate属性，用来指定在弹窗模式下的日期点击确认按钮时是否需要选择完整日期
## 1.4.9（2024-05-08）
更新域名
## 1.4.8（2024-04-22）
修复日历内外高度不一致的问题
## 1.4.7（2024-04-16）
增加 operationPosition 属性 用来控制弹窗日历取消和确认按钮的显示位置
## 1.4.6（2024-04-16）
新增属性 actBadgeColor, 当通过 `selected` 属性设置某个日期 `badgeColor`后，如果该日期被选择且主题色与 `badgeColor` 一致时，徽标会显示本颜色
## 1.4.5（2024-04-15）
1. 新增reset方法来重置日历数据
2. 新增插槽-operation 用来自定义弹窗日历确认和取消按钮
3. selected 属性新增 badgeColor 用来指定 badge 颜色
4. close 事件改为弹窗日历点击mask关闭时触发，新增cancel事件，弹窗日历点击取消时触发。
## 1.4.4（2024-01-19）
修复vue2初始化时使用同一引用类型造成的bug
## 1.4.3（2024-01-17）
优化切换折叠状态时的记录方式
## 1.4.2（2024-01-12）
优化	monthShowCurrentMonth 属性， 如果仅显示当月直接不展示该元素。
## 1.4.1（2024-01-12）
优化	monthShowCurrentMonth 属性， 如果仅显示当月直接不展示该元素。
## 1.4.0（2024-01-11）
修复type="week"时找不到
## 1.3.9（2024-01-10）
优化: 将日历日期宽度固定改为跟随父级来决定宽度，避免出现父元素宽度变化带来的对不齐
## 1.3.8（2024-01-04）
1. 增加节日
2. 日历增加自定义高度
3. vue页面日历折叠时增加动效
4. 增加头部插槽
5. 仅显示当月时自动扩大间距，不会再出现下面一排空白的尴尬情况了
## 1.3.7（2023-12-19）
修复回到今日时没有正确记录折叠日期
修复selected变化时无法正常更新打点信息
## 1.3.6（2023-12-13）
修复单选模式下选中时展示的错误的weeks
## 1.3.5（2023-12-13）
修复动态修改date以及其他会触发更新init的函数, swiper不对及只会更新一个下一个数据的bug
## 1.3.4（2023-12-08）
修复selectd没有启用深度监听，导致直接添加数组带来的异常
## 1.3.3（2023-11-23）
1. 修复weeks错误类型提示
2. 修复calendarChange返回month类型不对
## 1.3.2（2023-11-22）
修复将今日日期打点禁用后造成的bug
## 1.3.1（2023-11-21）
1. 修复wu-icon依赖缺失
2. 新增rangeHaveDisableTruncation属性, 用来指定范围选择遇到打点禁用日期是否进行截断
## 1.3.0（2023-11-19）
1. 日历新增类型（周、月日历）
2. 日历新增折叠功能 
3. 日历新增以周几开始的属性（周日、周一）
## 1.2.9（2023-11-08）
1. 修复mode变化时，不会正确重置的问题
2. 优化月份大文字显示方式
## 1.2.8（2023-10-22）
新增maskClick用来控制是否点击遮罩层关闭
新增disabledChoice用来控制是否禁止点击日历
## 1.2.7（2023-09-22）
修复传date值情况下不会默认选中的bug
## 1.2.6（2023-09-22）
修改useToday描述
## 1.2.5（2023-09-22）
新增useToday属性用来指定是否使用今天作为默认时间
## 1.2.4（2023-09-21）
修复插入模式下顶部会显示弹窗关闭的内容
## 1.2.3（2023-09-20）
修复恢复默认数据错误的bug
## 1.2.2（2023-09-19）
新增rangeSameDay属性用来指定选日期范围选择时起始日期是否可以为同一天
## 1.2.1（2023-09-18）
1.修复wu-calendar回到今日错误
2.优化wu-calendar picker日期与当前日历日期同步
3.wu-calendar新增mode属性，用来控制单日期、多日期、日期选择范围模式
4.优化wu-calendar date属性来更好的指定多日期、范围日期的默认值
## 1.2.0（2023-09-17）
修复date变化时未成功重置
## 1.1.9（2023-09-17）
1. 新增mode属性用来指定日期选择模式
2. 增加多选
3. 增加范围选择模式、多选模式默认值
## 1.1.8（2023-09-12）
修复回到今日错误
新增日历picker日期与日历同步
## 1.1.7（2023-09-11）
自定义事件声明
## 1.1.6（2023-09-09）
增加 `rangeEndRepick` 属性，用来指定选择完成后点击选择范围内的日期是否可以重选结束日期
## 1.1.5（2023-09-09）
修复每月仅显示当月数据时上方星期错乱的问题
## 1.1.4（2023-09-05）
1. 优化动态计算算法
2. 优化弹窗模式打开缓慢的问题
3. 优化Color方法
## 1.1.3（2023-09-03）
新增插件预览图片
## 1.1.2（2023-09-02）
1. 新增monthShowCurrentMonth 用来设置是否每月仅展示当月数据
2. 修复动态加载时下一月数据更新错误问题
3. 修复confirm和change事件选中的列表中有被禁止的日期的问题
## 1.1.1（2023-08-31）
修复在插入模式中无法滑动的bug
## 1.1.0（2023-08-29）
修复回到今日bug(来自群里464与A毛毛大佬的测试)
## 1.0.9（2023-08-29）
修复依赖缺失
## 1.0.8（2023-08-22）
修复v2初始化时使用不存在的属性导致不可使用的bug(来自wu-ui群内攸宁大佬的反馈)
## 1.0.7（2023-08-21）
阻止默认滑动事件执行
## 1.0.6（2023-08-21）
修复支付宝高度消失的问题
## 1.0.5（2023-08-21）
修改说明
## 1.0.4（2023-08-20）
去除误加打印
## 1.0.3（2023-08-20）
1. 修复初始化时下个月份日期计算不对的问题
2. 增加打点禁用、设置打点徽标位置
## 1.0.2（2023-08-20）
更新展示截图
## 1.0.1（2023-08-20）
修改展示截图及说明
## 1.0.0（2023-08-20）
动态滑动计算的日历插件，还可以设置滑动切换模式、自定义主题颜色、自定义文案、农历显示等功能，可以让您纵享丝滑的使用日历。
