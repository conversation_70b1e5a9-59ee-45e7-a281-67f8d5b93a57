<template>
  <view :class="customClass" class="my-chart" @touchend="isTouchMoving = false" @touchstart="isTouchMoving = true">
    <scroll-view id="chat-scroll-view" :scroll-top="scrollTop" :scroll-y="true" class="content" enable-flex enhanced
                 scroll-anchoring show-scrollbar="false" @scroll="onScroll">
      <view class="chat-box">
        <view v-if="showHello && chatInitData.logo && list.length === 1" class="top-logo">
          <image :src="chatInitData.logo" class="logo" mode="aspectFill"/>
          <view class="slogan">{{ chatInitData.slogan }}</view>
          <view class="prompts">
            <view v-for="(item, index) in chatInitData.prompts" :key="index" @tap="onQuick({ text: item })">
              <text>🔎</text>
              {{ item }}
            </view>
          </view>
        </view>

        <view v-for="(item, index) in list" :id="`chat-content-${index}`" :key="index" :class="item.role"
              class="chat-content">
          <view class="message-container">
            <view v-if="item.role == 'user'" class="content">{{ item.data.text }}</view>
            <template v-else>

              <view v-if="hasText(item)" class="content">
                <view v-if="item.data.thinking_text?.length" class="thinking">
                  <view :class="{
                    close: !item.data.thinking
                  }" class="title">{{ item.data.thinking ? '深度思考' : '已深度思考' }}：
                    <text class="iconfont icon-xiangshang" @tap="item.data.thinking = !item.data.thinking"/>
                  </view>
                  <view v-if="item.data.thinking" class="thinking-text">
                    <rich-text :nodes="deepThinkingText(item.data.thinking_text)"/>
                  </view>
                </view>

                <view v-if="item.data.text?.length > 0">
                  <MyContentRenderer :empty-configs="emptyStateConfigs" :is-ing="isIng"
                                     :is-last-message="index === list.length - 1"
                                     :show-fast-tips="!isIng && list.length > 3 && index === list.length - 1"
                                     :text="item.data.text"
                                     @force-submit="onForceSubmit"/>
                </view>
              </view>
              <HotelList v-else-if="item.data?.content_type === 'hotel'" :is-replace="showHotelReplaceBtn"
                         :markdown-text="item.data.text" :show-view-more="showViewMoreHotel"
                         @hotel-click="e => emit('hotelClick', e)" @view-more="e => onViewMoreHotel(e)"/>


              <!--              <ChatHotelCard v-if="item.data?.content_type === 'hotel'" :request-id="item.data.request_id"/>-->

              <view v-else-if="item.data.request_id" class="plan-item">
                <MyPlanCard :key="item.data.request_id + '_' + (item.data.unlocked ? 'unlocked' : 'locked')"
                            :need-deduct="1"
                            :request-id="item.data.request_id"
                            :show-replace="chatType === 'replan'" :unlocked="item.data.unlocked ?? true"
                            @imageClick="onUnlock(index)"
                            @load="scrollToBottom" @remove="onPlanRemove" @replace="e => emit('replan', e)"
                            @retry="onRetry"/>
              </view>
            </template>
          </view>
        </view>

        <!-- 添加独立的思考动画文本元素 -->
        <view v-if="isThinking" class="thinking-dots-container">
          <text class="thinking-text-animation">AI思考中</text>
          <text class="dot-animation dot1">.</text>
          <text class="dot-animation dot2">.</text>
          <text class="dot-animation dot3">.</text>
        </view>
      </view>

    </scroll-view>
    <!-- 建议菜单 -->
    <view v-if="currentSuggestions.length > 0" class="suggestions-container">
      <view class="suggestions-wrapper">
        <view v-for="(s, index) in currentSuggestions" :key="index"
              :class="['suggestion-item', { 'suggestion-fade-in': isShowingSuggestions && index === displayedSuggestionsCount - 1 }]"
              @tap="onQuick({ text: s })">
          {{ s }}
        </view>
      </view>
    </view>
    <view class="footer">
      <MyChatInputWithMenu :can-show-menu="canOpenMenu" :reinit="reinit" @discover="onDiscover" @done="onQuick"
                           @search-scenic="onSearchScenic" @search-hotel="onSearchHotel" @import-trip="onImportTrip"/>
    </view>

    <ImportTripPopup v-if="showImportPopup" @close="showImportPopup = false" @submit="handleImportSubmit"/>

    <MyActivityDoTaskPopup :data="doTaskData"></MyActivityDoTaskPopup>
    <MyPointNotEnough v-if="showPointNotEnough" @close="handlePointNotEnoughClose" @point-change="unlock()"/>
    <MyIOSPaymentAlert :visible="showIOSPaymentAlert" @close="handleIOSPaymentAlertClose"/>
  </view>
</template>

<script setup>
import {chatDetail, startNewChat, chatIndex} from '@/api/chat'
import MyChatInputWithMenu from '@/components/MyChatInputWithMenu/MyChatInputWithMenu.vue'
import MyPlanCard from '@/components/MyPlanCard/MyPlanCard.vue'
import ImportTripPopup from '@/components/ImportTripPopup/ImportTripPopup.vue'
import MyPointNotEnough from '@/components/MyPointNotEnough/MyPointNotEnough.vue'
import MyContentRenderer from '@/components/MyContentRenderer/MyContentRenderer.vue'
import {ChatStore} from './chatStore'
import {useUserStore} from '@/store/user'
import {useGlobalStore} from '@/store/global'
import {navTo, richtext, showToast} from '@/utils'
import {ChatRoleSystem, ChatRoleUser} from '@/utils/constmap'
import {computed, getCurrentInstance, nextTick, onMounted, onUnmounted, ref, watch} from 'vue'
import {trackEvent} from "@/utils/tracker";
import MyActivityDoTaskPopup from "@/components/MyActivityDoTaskPopup/MyActivityDoTaskPopup.vue";
import MyIOSPaymentAlert from "@/components/MyIOSPaymentAlert/MyIOSPaymentAlert.vue";
import {parseMarkdown} from "@/utils/markdown";
import {TaskCondTypePlan} from "@/utils/constmap_activity";
import {getGlobalData} from "@/utils/request";
import HotelList from "@/components/HotelList/HotelList.vue";

const props = defineProps({
  customClass: {
    type: String,
    default: ''
  },
  canOpenMenu: {
    type: Boolean,
    default: true
  },
  chatType: {
    type: String,
    default: '', // hotel | ''
  },
  showViewMoreHotel: {
    type: Boolean,
    default: true
  },
  showHotelReplaceBtn: {
    type: Boolean,
    default: false
  },
  showHello: {
    type: Boolean,
    default: true, // 是否显示欢迎语
  },
  modelValue: {
    type: Object,
    default() {
      return {} //一次性数据
    }
  },
  text: {
    type: String,
    default: ''
  },
  contextId: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['hotelClick', 'update:modelValue', 'replan'])

let isTouchMoving = false
const chatStore = new ChatStore()
const userStore = useUserStore()
const globalStore = useGlobalStore()
const isIng = ref(false)

// 空状态配置
const emptyStateConfigs = ref([
  {
    type: 'hotel',
    keywords: ['暂无'],
    icon: 'icon-queshengicon',
    title: '暂无符合的酒店',
    description: '抱歉，没有找到符合您需求的酒店。'
  },
  // 为将来扩展预留空间，可以添加更多类型
  // {
  //   type: 'scenic',
  //   keywords: ['暂无符合的景点'],
  //   icon: 'icon-queshengicon',
  //   title: '暂无符合的景点',
  //   description: '抱歉，没有找到符合您需求的景点。'
  // }
])
const chatInitData = ref({
  prompts: [],
  logo: '',
  slogan: '',
})

const reinit = ref(0)
const isThinking = ref(false)
const list = computed(() => {
  return chatStore.list.value
})
const doTaskData = ref({})
const instance = getCurrentInstance()

// 获取当前要显示的建议菜单
const currentSuggestions = computed(() => {
  // 如果正在逐个显示建议，返回部分建议
  if (isShowingSuggestions.value && allSuggestions.value.length > 0) {
    return allSuggestions.value.slice(0, displayedSuggestionsCount.value)
  }

  // 否则检查最后一条消息的建议
  if (list.value.length > 0) {
    const lastMessage = list.value[list.value.length - 1]
    if (lastMessage.role === 'system' && lastMessage.data?.suggestions?.length > 0) {
      return lastMessage.data.suggestions
    }
  }
  return []
})

const scrollTop = ref(0)
const thinkingIndex = ref(-1)
const showImportPopup = ref(false)

// 建议显示相关状态
const allSuggestions = ref([]) // 完整的建议数组
const displayedSuggestionsCount = ref(0) // 当前显示的建议数量
const isShowingSuggestions = ref(false) // 是否正在逐个显示建议
let suggestionTimer = null // 建议显示定时器
const unlockItemIndex = ref(-1) // 当前需要解锁的item的index
const isUnlockClicked = ref(false) // 是否点击了解锁
const currentRequestId = ref('') // 当前需要解锁的item的request_id
const showPointNotEnough = ref(false) // 是否显示积分不足弹窗
const showIOSPaymentAlert = ref(false) // 是否显示iOS支付提醒弹窗
const contextId = computed({
  get() {
    return globalStore.context_id
  },
  set(val) {
    globalStore.setContextId(val)
  }
})
let socketTask = null

// 清空数据的工具函数
function clearDataAfterUse() {
  emit('update:modelValue', {})
}

function onViewMoreHotel(data) {
  const params = {
    tab: 'hotel'
  }
  if (data.longitude && data.latitude) {
    params.location = `${data.longitude},${data.latitude}`
  } else if (data.firstHotel) {
    params.location = `${data.firstHotel.longitude},${data.firstHotel.latitude}}`
  }

  navTo('pages/search/search', params)
}

function onForceSubmit() {
  onQuick({
    text: '直接生成'
  }, true)
}

// 清理建议显示定时器
function clearSuggestionTimer() {
  if (suggestionTimer) {
    clearTimeout(suggestionTimer)
    suggestionTimer = null
  }
}

// 启动逐个显示建议的动画
function startShowingSuggestions(suggestions) {
  // 清理之前的定时器
  clearSuggestionTimer()

  // 重置状态
  allSuggestions.value = suggestions
  displayedSuggestionsCount.value = 0
  isShowingSuggestions.value = true

  // 逐个显示建议
  function showNextSuggestion() {
    if (displayedSuggestionsCount.value < allSuggestions.value.length) {
      displayedSuggestionsCount.value++
      suggestionTimer = setTimeout(showNextSuggestion, 300) // 每300ms显示一个
    } else {
      // 全部显示完成
      isShowingSuggestions.value = false
      suggestionTimer = null
    }
  }

  // 开始显示第一个建议
  showNextSuggestion()
}

function hasText(item) {
  if (item.data.content_type === 'hotel'
      && item.data.text
      && !emptyStateConfigs.value.some(v => v.keywords.some(vv => item.data.text.includes(vv)))) {
    return false
  }
  return item.data.need_more && (item.data.text || item.data.thinking_text)
}

function deepThinkingText(text) {
  let content = richtext(parseMarkdown(text))
  return `<div style="font-size: ${uni.rpx2px(24)}px;color: #666666;">${content}</div>`
}

function onRetry({travel_data}) {
  if (travel_data.prompt_options) {
    const {from, to, transport, accommodation, strength} = travel_data.prompt_options
    let days = travel_data.sections?.length || 0
    let text = `请再帮我重新生成行程,选项如下：
          出发地：${from}
          目的地:${to}`
    if (days > 0) {
      text += `\n预期天数:${days}`
    }
    if (transport) {
      text += `\n交通方式:${transport}`
    }
    if (accommodation) {
      text += `\n住宿方式:${accommodation}`
    }
    if (strength) {
      text += `\n节奏强度:${strength}`
    }
    onQuick({text})
  }
}

function addNewChat() {
  if (isIng.value) {
    return
  }

  // 清理建议显示状态
  clearSuggestionTimer()
  allSuggestions.value = []
  displayedSuggestionsCount.value = 0
  isShowingSuggestions.value = false

  // 清空聊天记录
  chatStore.clear()
  //清除页面传参

  // 调用newChat接口创建新的对话
  return startNewChat({chat_type: props.chatType}).then(({data}) => {
    // 如果接口返回了context_id，则更新contextId和globalStore中的context_id
    if (data && data.context_id) {
      contextId.value = data.context_id
    } else {
      // 如果接口没有返回context_id，则将contextId设置为空字符串
      contextId.value = ''
    }
    if (['hotel', 'replan'].includes(props.chatType)) {
      chatStore.addItem({text: data.system_prompt, need_more: true}, ChatRoleSystem)
      return
    }

    return getChatHistory(contextId.value)
  }).catch((error) => {
    console.error('创建新对话失败:', error)
    // 如果接口调用失败，则将contextId设置为空字符串
    contextId.value = ''
  })
}

function onPlanRemove({request_id}) {
  const index = list.value.findIndex((item) => item.data.request_id == request_id)
  if (index == -1) {
    return
  }
  chatStore.removeItem(index)
}

// 使用节流函数优化滚动事件处理
let scrollTimer = null
let socketUrl = ''
let pageData = {}

function onScroll(e) {
  // 如果滚动位置小于当前位置，不更新
  if (e.detail.scrollTop <= scrollTop.value) {
    return
  }

  // 使用节流函数，避免频繁更新导致的性能问题
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  scrollTimer = setTimeout(() => {
    scrollTop.value = e.detail.scrollTop
    scrollTimer = null
  }, 16) // 约60fps的更新频率
}

// 处理【您可以回复】标记的工具函数
function handleReplyMark(messageObj, text, isThinkingText = false, isLastMessage = true) {
  const parts = text.split('【您可以回复】');

  // 更新消息文本，只保留标记前的内容
  if (isThinkingText) {
    messageObj.data.thinking_text = parts[0];
  } else {
    messageObj.data.text = parts[0];
  }

  // 只有最后一条消息才处理建议部分
  if (parts.length > 1 && isLastMessage) {
    handleSuggestions(messageObj, parts[1]);
  }

  // 标记消息已被截断处理
  messageObj.data.truncated = true;
}

// 处理建议文本的工具函数
function handleSuggestions(messageObj, suggestionsText) {
  // 初始化suggestions数组，如果不存在
  if (!messageObj.data.suggestions) {
    messageObj.data.suggestions = [];
  }

  // 解析建议文本
  const suggestionsItems = suggestionsText.split('\n')
      .map(item => item.trim())
      .filter(item => item.length > 0);

  // 添加到建议数组
  if (suggestionsItems.length > 0) {
    messageObj.data.suggestions = [...messageObj.data.suggestions, ...suggestionsItems];
  }
}

// 处理单个内容中的【您可以回复】标记
function processReplyMarkInContent(content, isLastMessage = true) {
  if (!content || !content.includes('【您可以回复】')) {
    return {processedContent: content, suggestions: []};
  }

  const parts = content.split('【您可以回复】');
  const processedContent = parts[0]; // 只保留标记前的内容

  let suggestions = [];
  if (parts.length > 1 && isLastMessage) {
    // 只有最后一条消息才处理建议部分
    const suggestionsText = parts[1];
    suggestions = suggestionsText.split('\n')
        .map(item => item.trim())
        .filter(item => item.length > 0);
  }

  return {processedContent, suggestions};
}

// 分离思考过程和输出内容的工具函数
function separateThinkingContent(content) {
  if (!content) return {thinking_text: '', text: ''};

  // 按换行符分割内容
  const lines = content.split('\n');

  // 如果只有一行或没有内容，整个内容作为输出
  if (lines.length <= 1) {
    return {thinking_text: '', text: content};
  }

  // 找到最后一个非空行作为输出内容
  let outputText = '';
  let thinkingLines = [...lines];

  // 从后往前找第一个非空行
  for (let i = lines.length - 1; i >= 0; i--) {
    const trimmedLine = lines[i].trim();
    if (trimmedLine.length > 0) {
      outputText = lines[i];
      // 移除这一行，剩余的作为思考内容
      thinkingLines = lines.slice(0, i);
      break;
    }
  }

  // 合并思考内容
  const thinking_text = thinkingLines.join('\n');

  return {thinking_text, text: outputText};
}

async function onMessage(res) {
  const {code, data, msg} = res

  // console.log('onMessage:', res)

  if (code !== 0) {
    console.error('onMessage error:', res)

    if (code === 900018) {
      socketTask.close()
      isIng.value = false
      await addNewChat()

      pageData.context_id = contextId.value
      onQuick({text: pageData.text, url: pageData.url})
    } else {
      showToast(msg)
    }
    return
  }
  contextId.value = data.conversation_id
  const last = list.value[list.value.length - 1]
  last.data.need_more = true

  switch (data.event) {
    case 'node_started':
      if (data.data.title.includes('进行初步行程规划')) {
        last.data.content_type = 'plan'
      }
      if (data.data.title.includes('开始输出酒店数据')) {
        last.data.content_type = 'hotel'
      }
      break
    case 'message':
      last.data.thinking = !['plan', 'hotel'].includes(last.data.content_type)

      // 如果消息已经被截断，不再处理
      if (last.data.truncated) {
        break
      }

      // 先追加新内容
      if (last.data.thinking) {
        if (last.data.thinking_text === undefined) {
          last.data.thinking_text = ''
        }
        last.data.thinking_text += data.answer

        // 当接收到首条thinking_text时，隐藏思考动画
        if (last.data.thinking_text && isThinking.value) {
          isThinking.value = false
        }

        // 检查完整文本是否包含标记
        if (last.data.thinking_text.includes('【您可以回复】')) {
          handleReplyMark(last, last.data.thinking_text, true, true)
        }
      } else {
        if (last.data.text === undefined) {
          last.data.text = ''
        }

        // 当接收到任何内容时，隐藏思考动画
        if (isThinking.value) {
          isThinking.value = false
        }

        last.data.text += data.answer

        // 检查完整文本是否包含标记
        if (last.data.text.includes('【您可以回复】')) {
          handleReplyMark(last, last.data.text, false, true)
        }
      }

      scrollToBottom()
      break
    case 'yj_finish':
      contextId.value = data.data.context_id
      if (!data.data.need_more && last.data.content_type === 'plan') {
        trackEvent('chat_prompt_success')
        const lastIdx = list.value.length - 1
        chatStore.replaceItem(lastIdx, {request_id: data.data.request_id, need_deduct: 1}, ChatRoleSystem)
        doTaskData.value = {
          cond: TaskCondTypePlan,
          ai_reqid: data.data.request_id,
        }
      } else {
        // 思考完毕后自动折叠思考过程
        const lastMessage = list.value[list.value.length - 1]
        if (lastMessage && lastMessage.data && lastMessage.data.thinking_text) {
          lastMessage.data.thinking = false
        }
      }
      break
    case 'yj_suggestions':
      const {data: {suggestions}} = data
      if (suggestions.length > 0) {
        const last = list.value[list.value.length - 1]
        last.data.suggestions = suggestions

        // 启动逐个显示建议的动画
        startShowingSuggestions(suggestions)
        scrollToBottom()
      }
  }
}

async function onQuick(params, force = false) {
  isTouchMoving = false

  if (isIng.value) {
    showToast('请稍等，正在思考中...')
    return
  }
  if (params.url) {
    params.url = decodeURIComponent(params.url)
  }
  if (params.text) {
    params.text = decodeURIComponent(params.text)
  }

  // 支持直接传入字符串作为text参数（向后兼容）
  let text = '';
  let url = '';
  let context_id = contextId.value;
  console.log('赋值后的对话ID', context_id)
  if (typeof params === 'string') {
    text = params;
  } else {
    // 支持传入对象参数
    text = params.text || '';
    url = params.url || '';
    // 如果传入了context_id，则使用传入的值，否则使用当前值
    if (params.context_id && context_id == '') {
      context_id = params.context_id;
    }
  }

  if (!force && text.length == 0 && (!url || url.length == 0)) {
    showToast('请输入您的旅行需求。')
    return
  }

  pageData = {}
  Object.assign(pageData, getGlobalData(), {
    text,
    force: force ? 1 : 0,
    url,
    context_id,
    request_id: params.request_id || '',
    chat_type: props.chatType
  }, props.modelValue)

  // 使用完数据后立即清空
  clearDataAfterUse()

  const location = await globalStore.getLocation()
  pageData.location = location?.address || ''

  chatStore.addItem({text}, ChatRoleUser)
  // 设置思考动画显示
  isThinking.value = true

  // 添加"思考中..."的系统消息
  chatStore.addItem({text: ""}, ChatRoleSystem, {type: 'thinking'})
  thinkingIndex.value = chatStore.list.length - 1

  trackEvent('click_chat_prompt')
  socketUrl = buildSocketUrl()

  createWebSocket()
}

function buildSocketUrl() {
  let socketUrl = import.meta.env.VITE_API_BASE_URL + '/api/v1/front/chat/prompt_stream'
  socketUrl = socketUrl.replace(/^https?:\/\//, (mat) => {
    if (mat === 'http://') {
      return 'ws://'
    } else {
      return 'wss://'
    }
  })
  socketUrl += `?token=${userStore.token}`

  return socketUrl
}

function createWebSocket() {
  isIng.value = true
  socketTask = uni.connectSocket({
        url: socketUrl,
        success: () => {
        }
      }
  )
  socketTask.onError(e => {
    console.log('websocket error', e)
    isThinking.value = false
  })
  socketTask.onClose(e => {
    isIng.value = false
    isThinking.value = false
    console.log('websocket close')
  })
  socketTask.onMessage(({data}) => {
    onMessage(JSON.parse(data))
  })
  socketTask.onOpen(e => {
    for (let key in pageData) {
      if (typeof pageData[key] == 'string') {
        pageData[key] = decodeURIComponent(pageData[key])
      }
    }
    console.log('websocket open', pageData)
    socketTask.send({data: JSON.stringify(pageData)})
  })
}

function onSearchScenic() {
  navTo('pages/search/search', {tab: 'scenic'})
}

function onSearchHotel() {
  navTo('pages/search/search', {tab: 'hotel'})
}

function onDiscover() {
  navTo('pages/discover/discover')
}

function onImportTrip() {
  showImportPopup.value = true
}

function handleImportSubmit(text) {
  if (text) {
    // 使用正则表达式匹配URL，确保包含#符号及其后面的部分
    const urlRegex = /(?:(?:https?|ftp):\/\/)?(?:www\.)?(?:[-a-zA-Z0-9@:%._\+~#=]{1,256}\.)+[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)(?:#[-a-zA-Z0-9()@:%_\+.~#?&//=]*)?/gi;
    const matches = text.match(urlRegex);

    let content;
    let url = '';

    // 如果找到URL，则使用第一个URL
    if (matches && matches.length > 0) {
      url = matches[0];
      content = url;
    } else {
      // 如果不包含URL且文本长度超过50个汉字，则截取前50个汉字
      const chineseChars = text.match(/[\u4e00-\u9fa5]/g);
      if (chineseChars && chineseChars.length > 50) {
        // 找出前50个汉字在原文中的位置
        let count = 0;
        let endIndex = 0;
        for (let i = 0; i < text.length; i++) {
          if (/[\u4e00-\u9fa5]/.test(text[i])) {
            count++;
            if (count === 50) {
              endIndex = i + 1;
              break;
            }
          }
        }
        content = text.substring(0, endIndex);
      } else {
        content = text;
      }
    }

    // 使用新的对象参数格式调用onQucik
    // 如果URL存在，则不设置text，让onQucik方法处理
    if (url && url.length > 0) {
      onQuick({
        url: url,
        text: `帮我解析行程：${url}`
      })
    } else {
      onQuick({
        text: `${content}`
      })
    }
  }
  showImportPopup.value = false
}

async function onUnlock(index) {
  // 先重置状态，确保能够触发响应式更新
  showPointNotEnough.value = false

  // 使用 nextTick 确保在下一个 DOM 更新周期设置为 true
  nextTick(() => {
    // 弹出积分不足弹窗
    showPointNotEnough.value = true
    console.log('设置showPointNotEnough为true')
  })

  // 获取当前需要解锁的item的request_id
  if (list.value[index] && list.value[index].data && list.value[index].data.request_id) {
    const requestId = list.value[index].data.request_id
    currentRequestId.value = requestId
    console.log('当前request_id:', requestId)

    // 保存当前点击的item和request_id，用于onShow中设置解锁状态
    unlockItemIndex.value = index
    isUnlockClicked.value = true
  }
}

function scrollToBottom() {
  if (isTouchMoving) {
    return
  }

  // 使用nextTick确保DOM已更新
  nextTick(() => {
    // 获取聊天内容的高度
    const query = uni.createSelectorQuery().in(instance.proxy);
    query.select('.chat-box').boundingClientRect(data => {
      if (data) {
        // 使用setTimeout确保在所有渲染完成后执行滚动
        setTimeout(() => {
          // 设置scrollTop为内容高度，实现滚动到底部
          scrollTop.value = data.height + 10; // 添加额外高度确保滚动到底部
        }, 100);
      }
    }).exec();
  });
}

function handlePointNotEnoughClose() {
  console.log('关闭积分不足弹窗')
  // 关闭积分不足弹窗
  showPointNotEnough.value = false
  // 重置解锁点击状态，确保下次点击可以正常显示弹窗
  isUnlockClicked.value = false
  console.log('重置完成，showPointNotEnough:', showPointNotEnough.value, 'isUnlockClicked:', isUnlockClicked.value)
}

async function unlock() {
  reinit.value++
  if (!doLoaded.value) {
    await doLoad(Object.assign({}, props.modelValue))
    // 使用完数据后立即清空
    clearDataAfterUse()
    doLoaded.value = true
  }

  // 关闭积分不足弹窗
  showPointNotEnough.value = false

  // 处理解锁逻辑：如果之前点击了解锁，则更新该item的解锁状态
  if (isUnlockClicked.value && unlockItemIndex.value >= 0) {
    // 找到需要更新的项
    const index = unlockItemIndex.value
    const item = list.value[index]

    if (item && item.data && item.data.request_id) {
      // 创建一个新的数据对象，包含原有数据和更新的状态
      const updatedData = {
        ...item.data,
        unlocked: false
      }

      // 使用 replaceItem 方法更新数据
      chatStore.replaceItem(index, updatedData, item.role)
      console.log('已更新解锁状态，index:', index)
    }

    // 重置状态
    isUnlockClicked.value = false
    unlockItemIndex.value = -1
  }
}

// iOS支付提醒弹窗相关处理函数
function handleIOSPaymentAlertClose() {
  console.log('关闭iOS支付提醒弹窗')
  showIOSPaymentAlert.value = false
}

async function getChatHistory(context_id) {
  if (['hotel', 'replan'].includes(props.chatType)) {
    return
  }
  console.log("对话id",context_id)
  // 调用 chatDetail API 获取会话记录
  try {
    const response = await chatDetail({context_id: context_id})
    if (response.data && response.data.list && Array.isArray(response.data.list)) {
      // 清空当前聊天记录
      chatStore.clear()

      // 将返回的会话记录添加到 chatStore
      response.data.list.forEach((item, index) => {
        if (item.role === 'user') {
          chatStore.addItem({text: item.content}, ChatRoleUser)
        } else if (item.role === 'system') {
          if (!item.need_more) {
            // 添加unlocked字段，默认为true
            chatStore.addItem({
              request_id: item.request_id,
              content_type: item.content_type,
              text: item.content,
              need_more: item.need_more,
              unlocked: true
            }, ChatRoleSystem)
          } else {
            // 判断是否为最后一条消息
            const isLastMessage = index === response.data.list.length - 1;

            // 先处理【您可以回复】标记
            const {processedContent, suggestions} = processReplyMarkInContent(item.content, isLastMessage);

            // 再分离思考过程和输出内容
            const {thinking_text, text} = separateThinkingContent(processedContent);

            // 构建消息数据
            const messageData = {
              thinking_text,
              text,
              content_type: item.content_type,
              need_more: item.need_more,
              thinking: false // 历史消息的思考过程默认折叠
            };
            // 如果是最后一条消息且有建议，添加到消息数据中
            if (isLastMessage && suggestions.length > 0) {
              messageData.suggestions = suggestions;
            }

            chatStore.addItem(messageData, ChatRoleSystem, item.option)
          }
        }
      })

      // 历史消息的【您可以回复】标记已在添加时处理完成
    }
    // 在历史记录加载完成后，明确调用scrollToBottom
    // 使用setTimeout确保所有DOM更新已完成
    setTimeout(() => {
      scrollToBottom()
    }, 100)
  } catch (error) {
    console.error('获取会话记录失败:', error)
  }
}

// const pageQuery = computed(() => {
//   return { text: props.text, context_id: props.contextId }
// })

const doLoaded = ref(false)
const doLoad = async (query = {}) => {
  // 如果URL中有context_id参数，优先使用URL中的
  contextId.value = query.context_id || ''

  if (contextId.value) {
    await getChatHistory(contextId.value)
  } else {
    await addNewChat()
  }

  if (query.text || query.url) {
    // 使用新的对象参数格式调用onQucik
    onQuick({
      text: query.text ? decodeURIComponent(query.text) : '',
      url: query.url || '',
      context_id: contextId.value
    })
  }
}

function stopsocketTask() {
  if (socketTask) {
    socketTask.close()
  }
  // 重置思考动画状态
  isThinking.value = false
  // 清理建议显示定时器
  clearSuggestionTimer()
}

onUnmounted(() => {
  stopsocketTask()
})

onMounted(async () => {
  try {
    const {data} = await chatIndex()
    Object.assign(chatInitData.value, data)
  } catch (error) {
    console.error('Failed to fetch chat index:', error)
  }
  await unlock()
})

defineExpose({
  unlock,
  addNewChat
})

</script>

<style lang="scss" scoped>
@import 'my-chat';
</style>
