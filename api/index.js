import request from '../utils/request.js'


//心愿点赞
export function like(data) {
  return request({
    url: '/api/v1/front/like',
    method: 'post',
    data,
  })
}

export function plans(data) {
  return request({
    url: '/api/v1/front/plans',
    method: 'get',
    data
  })
}

export function stations(data) {
  return request({
    url: '/api/v1/front/station',
    method: 'get',
    data
  })
}

export function holiday(params, loading = false) {
  return request({
    url: '/api/v1/front/holiday',
    method: 'get',
    data: params
  }, loading)
}

export function index() {
  return request({
    url: '/api/v1/front/index',
    method: 'get',
  })
}

export function zones() {
  return request({
    url: '/api/v1/front/zones',
    method: 'get',
  })
}

export function init() {
  return request({
    url: '/api/v1/front/init',
    method: 'get',
  })
}

export function login(data) {
  return request({
    url: '/api/v1/front/login',
    method: 'post',
    data
  })
}

export function usercenter(loading = true) {
  return request({
    url: '/api/v1/front/user/center',
    method: 'get'
  }, loading)
}

export function routeV2(data, loading = true) {
  return request({
    url: '/api/v1/front/plan/waymap_v2',
    method: 'get',
    data
  }, loading)
}

export function searchPoi(data) {
  return request({
    url: '/api/v1/front/search/poi',
    method: 'get',
    data
  })
}

export function uploadToken(data) {
  return request({
    url: '/api/v1/front/upload_token',
    method: 'get',
    data
  }, false)
}

export function geo(data) {
  return request({
    method: 'get',
    url: '/api/v1/front/geo',
    data,
  })
}

export function thirdRelUser(data) {
  return request({
    method: 'get',
    url: '/api/v1/front/third_rel_user',
    data,
  })
}

export function citiesList() {
  return request({
    method: 'get',
    url: '/api/v1/front/cities',
  })
}
