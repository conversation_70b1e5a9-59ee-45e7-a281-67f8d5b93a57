<template>
	<NavBar title=''></NavBar>
	<view class="container">
		<!-- 轮播图 -->
		<view class="hotel-banner">
			<swiper autoplay="true">
				<swiper-item v-for="(item, index) in [hotel.pic]" :key="index">
					<image :src="item"></image>
				</swiper-item>
			</swiper>
		</view>
		<view class="hotel-cont">
			<view class="title">
				{{dayjs(checkin).format('MM月DD日')}}-{{dayjs(checkout).format('MM月DD日')}}
				<view class="hotel-name">{{detail.title}}</view>
			</view>
			<view>
				{{room.name}} | {{room.bed_desc}}
			</view>
			<view>
				<view class="tag cancel" v-if="policy.cancel_desc">{{policy.cancel_desc}}</view>
				<view class="tag primary" v-if="policy.confirm_desc">{{policy.confirm_desc}}</view>
			</view>
		</view>

		<view class="card notice">
			<view class="title">必读须知</view>
			<view class="content">
				<view class="" v-if="policy.cancel_desc">{{policy.cancel_desc}}</view>
				<view class="" v-if="policy.confirm_desc">{{policy.confirm_desc}}</view>
			</view>
		</view>
		<view class="card num-info">
			<view class="content">
				<view class="room-number">
					<view class="left">
						<text class="title">订房信息</text>
						<template v-if="policy.max_amount">（最多{{policy.max_amount}}间）</template>
						<text v-if="policy.remain_rooms" class="remain-rooms">仅剩{{policy.remain_rooms}}间</text>
					</view>
					<view class="right">
						<uni-number-box :max="max" min="1" v-model="num"></uni-number-box>
					</view>
				</view>
			</view>
		</view>

		<view class="card card-preview">
			<view class="title">旅客信息（只需填写每个房间一位入住人姓名）</view>

			<view class="content">
				<uni-forms ref="formRef" :model-value="form" :rules="rules">
					<uni-forms-item :label="`入住人${index+1}`" v-for="(item, index) in form.peoples" :key="index">
						<uni-easyinput v-model="form.peoples[index]" :placeholder="`请填写入住人${index+1}姓名`"></uni-easyinput>
					</uni-forms-item>
					<uni-forms-item name="tel" label="联系电话">
						<uni-easyinput placeholder="请填写联系人电话" v-model="form.tel"></uni-easyinput>
					</uni-forms-item>
				</uni-forms>
			</view>

		</view>

		<view class="invalid-data" v-if="!canBuy">
			<view v-for="item in invalidPriceDaily" :key="item.date">
				{{formatTime(item.date, 'MM.DD')}} 不可预定
			</view>
		</view>

		<view class="footer">
			<view class="left">
				<view class="price">{{formatMoney(detail.total_price)}}</view>
				<view @tap="showDaily = true" class="view-daily" v-if="policy.price_daily">明细<text
						class="iconfont icon-xiangshang"></text>
				</view>

			</view>

			<view @tap="onSubmit" class="btn danger apply" :class="{disable: !canBuy}">
				<view>去支付</view>
			</view>
		</view>

		<MyHotelRoomPriceDaily v-if="showDaily" @close="showDaily = false" :total-price="detail.total_price" :num="num"
			:policy="policy"></MyHotelRoomPriceDaily>
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		onMounted,
		watch
	} from 'vue';

	import qs from 'qs'
	import dayjs from 'dayjs';
	import {
		orderPay,
		orderPreview,
		orderSubmit
	} from '../../api/order';

	import {
		formatMoney,
		formatTime,
		showToast
	} from '../../utils/index.js'
	import {
		doPay
	} from '../../utils/pay';
	import {
		hotelDetail
	} from '../../api/hotel';

	const props = defineProps({
		query: {
			type: Object,
			default: () => {
				return {
					checkin: '',
					checkout: '',
					hotel_id: '',
					ota_id: '',
					ota_code: '',
					policy_id: '',
					product_type: '',
					room_id: '',
					num: 1,
				}
			}
		}
	})

	const checkin = ref('')
	const checkout = ref('')
	const num = ref(props.query.num)
	const showDaily = ref(false)
	const hotel = ref({})

	const detail = ref({
		title: '',
		total_price: 0,
		sub_order: [{
			hotel_room: {
				attrs: [],
				policy: []
			}
		}]
	})

	const form = ref({
		tel: '',
		peoples: [],
	})
	const formRef = ref(null)
	const rules = computed(() => {
		const r = {
			tel: {
				rules: [{
					required: true,
					errorMessage: '请填写联系电话'
				}]
			}
		}

		return r
	})

	const invalidPriceDaily = computed(() => {
		if (!policy.value.price_daily) {
			return []
		}

		return policy.value.price_daily.filter(item => item.valid == false)
	})

	const canBuy = computed(() => {
		return invalidPriceDaily.value.length == 0
	})

	const room = computed(() => {
		if (detail.value.sub_order.length > 0) {
			const item = detail.value.sub_order[0].hotel_room
			item.attrs.push(item.name)

			return item
		}

		return {}
	})
	const policy = computed(() => {
		return room.value.policy.length > 0 ? room.value.policy[0] : {}
	})
	const max = computed(() => Math.min(policy.value.max_amount, policy.value.remain_rooms))

	watch(num, (newVal) => {
		initHotel(props.query)

		const pL = form.value.peoples.length
		if (newVal < pL) {
			form.value.peoples.splice(newVal, pL - newVal)
		} else if (newVal > pL) {
			form.value.peoples.push(...new Array(newVal - pL).fill(''))
		}
	})

	function onSubmit() {
		if (!canBuy.value) {
			return
		}
		formRef.value.validate().then(async (res) => {
			const index = form.value.peoples.findIndex(item => item.length == 0)
			if (index > -1) {
				showToast('请填写入住人姓名', 'none')
				return
			}

			const data = {
				order_type: 'hotel',
				product_id: props.query.hotel_id,
				book_num: Number(num.value),
				products: packProducts(),
				name: form.value.peoples[0],
				tel: form.value.tel,
				total_price: detail.value.total_price,
			}
			data.products[0].peoples = form.value.peoples.map(name => {
				return {
					name
				}
			})
			data.product_type = props.query.product_type
			data.peoples = JSON.stringify(data.peoples)
			data.products = JSON.stringify(data.products)

			const resp = await orderSubmit(data)
			const {
				order_id,
				need_pay
			} = resp.data

			if (!need_pay) {
				showToast('下单成功').then(res => {
					uni.redirectTo({
						url: '/pages/orders/orderdetail?id=' + order_id
					})
				})
				return
			}

			uni.navigateTo({
				url: '/pages/pay/pay?order_id=' + order_id
			})
		})
	}

	function packProducts() {
		return [{
			product_type: props.query.product_type,
			product_id: Number(props.query.hotel_id),
      sale_price: policy.value.sales_price,
			num: Number(num.value),
			extra: {
				start: Number(props.query.checkin),
				end: Number(props.query.checkout),
				ota_id: props.query.ota_id,
				ota_room_id: props.query.room_id,
				ota_policy_id: decodeURIComponent(props.query.policy_id),
			}
		}]
	}

	function initHotel(query) {
		const data = {
			products: packProducts()
		}
		data.products = JSON.stringify(data.products)

		orderPreview(data).then(res => {
			const {
				data
			} = res

			Object.assign(detail.value, data)
		}).catch(() => {
			uni.navigateBack()
		})
	}

	onMounted(() => {
		checkin.value = dayjs.unix(props.query.checkin).toDate()
		checkout.value = dayjs.unix(props.query.checkout).toDate()

		form.value.peoples = new Array(Number(props.query.num)).fill('')

		hotelDetail(props.query.hotel_id).then(({
			data
		}) => {
			Object.assign(hotel.value, data)
		})

		initHotel(props.query)
	})
</script>

<style lang="scss" scoped>
	@import 'MyOrderPiewviewHotel.scss'
</style>